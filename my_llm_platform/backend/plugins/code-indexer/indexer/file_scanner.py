# ==============================================================
"""ディレクトリ走査 & .gitignore 対応ハッシュ計算。"""

from __future__ import annotations
import hashlib
import os
from pathlib import Path
from typing import Dict, Iterable, Set

import pathspec  # type: ignore[import]

DEFAULT_HASH_ALG = "sha256"
IGNORE_FILE_NAME = ".gitignore"


class DirectoryScanner:
    """対象ディレクトリを走査し、ファイルハッシュ辞書を返すクラス。"""

    def __init__(self, root: str | Path):
        self.root = Path(root).resolve()
        self.spec = self._load_gitignore()

    # ────────────────────────────────────────
    # パブリック API
    # ────────────────────────────────────────
    def scan(self) -> Dict[str, str]:
        """root 配下ファイルを走査し {path: hash} を返却。"""
        result: Dict[str, str] = {}
        for f in self._iter_files():
            result[str(f)] = self._file_hash(f)
        return result

    # ────────────────────────────────────────
    # 内部処理
    # ────────────────────────────────────────
    def _load_gitignore(self):
        ignore_path = self.root / IGNORE_FILE_NAME
        if ignore_path.exists():
            with ignore_path.open("r", encoding="utf-8") as fp:
                return pathspec.PathSpec.from_lines("gitwildmatch", fp)
        return pathspec.PathSpec([])

    def _iter_files(self) -> Iterable[Path]:
        for dirpath, _dirnames, filenames in os.walk(self.root):
            for name in filenames:
                full_path = Path(dirpath) / name
                rel_path = full_path.relative_to(self.root)
                if self.spec.match_file(str(rel_path)):
                    continue
                yield full_path

    def _file_hash(self, path: Path, algorithm: str = DEFAULT_HASH_ALG) -> str:
        hasher = hashlib.new(algorithm)
        with path.open("rb") as fp:
            for chunk in iter(lambda: fp.read(8192), b""):  # 8 KB
                hasher.update(chunk)
        return hasher.hexdigest()


