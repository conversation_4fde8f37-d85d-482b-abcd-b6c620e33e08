# ==============================================================
"""CLI エントリポイント。

Usage::
    poetry run python scripts/index_cli.py init  --path /repo  --index faiss.idx
    poetry run python scripts/index_cli.py watch --path /repo  --index faiss.idx
    poetry run python scripts/index_cli.py query --index faiss.idx "クエリ文"
"""

from __future__ import annotations
import argparse
from pathlib import Path
from typing import List

import numpy as np
import faiss  # type: ignore[import]

from ..indexer.index_updater import CodeIndexer
from ..indexer.embedder import Embedder


def _init(args):
    ci = CodeIndexer(args.path, args.index)
    ci.full_build()
    print("✅ Full indexing completed.")


def _watch(args):
    ci = CodeIndexer(args.path, args.index)
    ci.watch()


def _query(args):
    embedder = Embedder()
    qv = embedder.embed([args.query])
    index = faiss.read_index(str(args.index))
    D, I = index.search(qv, k=5)  # 近傍 5 件
    print("🔍 Top candidates →", I[0])


def main(argv: List[str] | None = None):
    parser = argparse.ArgumentParser()
    sub = parser.add_subparsers(dest="cmd", required=True)

    p_init = sub.add_parser("init")
    p_init.add_argument("--path", required=True, type=Path)
    p_init.add_argument("--index", required=True, type=Path)
    p_init.set_defaults(func=_init)

    p_watch = sub.add_parser("watch")
    p_watch.add_argument("--path", required=True, type=Path)
    p_watch.add_argument("--index", required=True, type=Path)
    p_watch.set_defaults(func=_watch)

    p_query = sub.add_parser("query")
    p_query.add_argument("--index", required=True, type=Path)
    p_query.add_argument("query", help="検索クエリ")
    p_query.set_defaults(func=_query)

    args = parser.parse_args(argv)
    args.func(args)


if __name__ == "__main__":
    main()


